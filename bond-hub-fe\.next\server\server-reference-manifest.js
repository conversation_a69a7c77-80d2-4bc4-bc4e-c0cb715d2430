self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"78baaf161d8042c9e43b7b7bb583643b5db64d979c\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"008673cce41bb0748eeab0a8c5e15dd0e75c470a57\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"401bcb71157084d0075f38b4fab8fe15fcf8850130\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"40ec1952e202d8247ec76d2390316778d2ce7fa377\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"4098ff5ec40c82b84b914242c9c8e2342e0f7e7011\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60fc4da9ba7554ee57dffc1a9aeb1bd63b367cadef\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"609363dbe2ab0a7be3aeff4f1f9ec2009a8ea1ff1f\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60dc3fc508290f0a1a4a0ce56c50ff217cca29ff89\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60cc36a9617ff5c1e95510b312570de436038e99b2\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"609119037dde07d36262480f3a817af7541b9401f7\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"403b4f755bd45270e8902cbcc28c881a2278982ae1\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"408809a4a3ab8a0071d8f9d5d13086eaac57369098\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60a21f35f94c504d3b6d7e8a58f244ac0c105a2c94\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"7c3899f513a3c864a5e5b631b1a9901a668d9ef142\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"0028ab17a9945dba8e0e548d1461a3410cfa0027a8\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"4062c51d7c82f2e7b6d92bafacff539d8e0ce748e8\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60fa044b243b8e5151bfa97646a72e11b6ad3f9960\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"6029a7dd8ad6febe6e0880eedceb645928a9d3c56d\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"706f71dcb4d30495fc248f384c877833675de1b1c6\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60aab3db73c1ff8b7cfdb524be03573ce3a3777681\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"6052e4293611abe98fa2f9431a68e0e94bf15660db\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"70d1c06c4a6971ab7ea7d684ace8c48fb398d39326\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60da15946eb95bbcf3148074030365d3a1d193cb53\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"40bfa86040b871c222d7bdce3b48b2866edaeca895\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(protected)/dashboard/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\",\n        \"app/(protected)/dashboard/contact/page\": \"action-browser\"\n      }\n    },\n    \"60839d57a5fda09a78ee332e851bea4ff208f13ec9\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"70e6a0dfc62b296f7a439765e4fa1cf66edbc3a8c5\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    },\n    \"60d94daf14faa77f77c90a33473fb3427cd9032c21\": {\n      \"workers\": {\n        \"app/(protected)/dashboard/chat/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(protected)/dashboard/chat/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(protected)/dashboard/chat/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"k1SfXOPbYPFKfFoGqJLAvU6urYRu/LIeEOUXx1BMBxM=\"\n}"